// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import { ActionUtils } from '../utils/ActionUtils';
import { AppConstants } from '../utils/constants';
import { isInWeixin, pageTo, $_share, getImageURL } from '../utils/generalUtil';
import { globalVariables } from '../utils/GlobalVariables';
// import GlobalDialog from '../globalComponet/GlobalDialog';
const { ccclass, property } = cc._decorator;
@ccclass
export default class NewClass extends cc.Component {
  @property(cc.Node)
  lose: cc.Node = null;

  duration: number = 0.8;

  onEnable() {
    // 根据globalVariables.fortuneSticksNum的值决定使用保存的签还是随机生成
    let num: number;
    if (globalVariables.fortuneSticksNum !== null) {
      // 如果有保存的幸运签，使用保存的值
      num = globalVariables.fortuneSticksNum;
    } else {
      // 如果没有保存的幸运签，随机生成一个并保存
      num = Math.floor(Math.random() * 6) + 1;
      globalVariables.fortuneSticksNum = num;
    }

    cc.resources.load(
      `drawLots/luckyDraw${num}`,
      cc.SpriteFrame,
      (err, spriteFrame) => {
        if (err) {
          cc.error('加载图片失败: ' + err);
          return;
        }
        // 使用 spriteFrame
        this.lose.getComponent(cc.Sprite).spriteFrame =
          spriteFrame as cc.SpriteFrame;
        this.lose.active = true;
      }
    );
  }

  start() {}

  // update (dt) {}
}
